{"name": "isexe", "version": "3.1.1", "description": "Minimal module to check if a file is executable.", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "types": "./dist/cjs/index.js", "files": ["dist"], "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}, "./posix": {"import": {"types": "./dist/mjs/posix.d.ts", "default": "./dist/mjs/posix.js"}, "require": {"types": "./dist/cjs/posix.d.ts", "default": "./dist/cjs/posix.js"}}, "./win32": {"import": {"types": "./dist/mjs/win32.d.ts", "default": "./dist/mjs/win32.js"}, "require": {"types": "./dist/cjs/win32.d.ts", "default": "./dist/cjs/win32.js"}}, "./package.json": "./package.json"}, "devDependencies": {"@types/node": "^20.4.5", "@types/tap": "^15.0.8", "c8": "^8.0.1", "mkdirp": "^0.5.1", "prettier": "^2.8.8", "rimraf": "^2.5.0", "sync-content": "^1.0.2", "tap": "^16.3.8", "ts-node": "^10.9.1", "typedoc": "^0.24.8", "typescript": "^5.1.6"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tsc -p tsconfig/cjs.json && tsc -p tsconfig/esm.json && bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc --tsconfig tsconfig/esm.json ./src/*.ts"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "tap": {"coverage": false, "node-arg": ["--enable-source-maps", "--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "repository": "https://github.com/isaacs/isexe", "engines": {"node": ">=16"}}
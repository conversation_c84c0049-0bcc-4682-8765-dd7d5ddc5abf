"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: events.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.CloudEventBatch = exports.CloudEvent_CloudEventAttributeValue = exports.CloudEvent_AttributesEntry = exports.CloudEvent = void 0;
/* eslint-disable */
const any_1 = require("./google/protobuf/any");
const timestamp_1 = require("./google/protobuf/timestamp");
exports.CloudEvent = {
    fromJSON(object) {
        return {
            id: isSet(object.id) ? globalThis.String(object.id) : "",
            source: isSet(object.source) ? globalThis.String(object.source) : "",
            specVersion: isSet(object.specVersion) ? globalThis.String(object.specVersion) : "",
            type: isSet(object.type) ? globalThis.String(object.type) : "",
            attributes: isObject(object.attributes)
                ? Object.entries(object.attributes).reduce((acc, [key, value]) => {
                    acc[key] = exports.CloudEvent_CloudEventAttributeValue.fromJSON(value);
                    return acc;
                }, {})
                : {},
            data: isSet(object.binaryData)
                ? { $case: "binaryData", binaryData: Buffer.from(bytesFromBase64(object.binaryData)) }
                : isSet(object.textData)
                    ? { $case: "textData", textData: globalThis.String(object.textData) }
                    : isSet(object.protoData)
                        ? { $case: "protoData", protoData: any_1.Any.fromJSON(object.protoData) }
                        : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== "") {
            obj.id = message.id;
        }
        if (message.source !== "") {
            obj.source = message.source;
        }
        if (message.specVersion !== "") {
            obj.specVersion = message.specVersion;
        }
        if (message.type !== "") {
            obj.type = message.type;
        }
        if (message.attributes) {
            const entries = Object.entries(message.attributes);
            if (entries.length > 0) {
                obj.attributes = {};
                entries.forEach(([k, v]) => {
                    obj.attributes[k] = exports.CloudEvent_CloudEventAttributeValue.toJSON(v);
                });
            }
        }
        if (message.data?.$case === "binaryData") {
            obj.binaryData = base64FromBytes(message.data.binaryData);
        }
        else if (message.data?.$case === "textData") {
            obj.textData = message.data.textData;
        }
        else if (message.data?.$case === "protoData") {
            obj.protoData = any_1.Any.toJSON(message.data.protoData);
        }
        return obj;
    },
};
exports.CloudEvent_AttributesEntry = {
    fromJSON(object) {
        return {
            key: isSet(object.key) ? globalThis.String(object.key) : "",
            value: isSet(object.value) ? exports.CloudEvent_CloudEventAttributeValue.fromJSON(object.value) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.key !== "") {
            obj.key = message.key;
        }
        if (message.value !== undefined) {
            obj.value = exports.CloudEvent_CloudEventAttributeValue.toJSON(message.value);
        }
        return obj;
    },
};
exports.CloudEvent_CloudEventAttributeValue = {
    fromJSON(object) {
        return {
            attr: isSet(object.ceBoolean)
                ? { $case: "ceBoolean", ceBoolean: globalThis.Boolean(object.ceBoolean) }
                : isSet(object.ceInteger)
                    ? { $case: "ceInteger", ceInteger: globalThis.Number(object.ceInteger) }
                    : isSet(object.ceString)
                        ? { $case: "ceString", ceString: globalThis.String(object.ceString) }
                        : isSet(object.ceBytes)
                            ? { $case: "ceBytes", ceBytes: Buffer.from(bytesFromBase64(object.ceBytes)) }
                            : isSet(object.ceUri)
                                ? { $case: "ceUri", ceUri: globalThis.String(object.ceUri) }
                                : isSet(object.ceUriRef)
                                    ? { $case: "ceUriRef", ceUriRef: globalThis.String(object.ceUriRef) }
                                    : isSet(object.ceTimestamp)
                                        ? { $case: "ceTimestamp", ceTimestamp: fromJsonTimestamp(object.ceTimestamp) }
                                        : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.attr?.$case === "ceBoolean") {
            obj.ceBoolean = message.attr.ceBoolean;
        }
        else if (message.attr?.$case === "ceInteger") {
            obj.ceInteger = Math.round(message.attr.ceInteger);
        }
        else if (message.attr?.$case === "ceString") {
            obj.ceString = message.attr.ceString;
        }
        else if (message.attr?.$case === "ceBytes") {
            obj.ceBytes = base64FromBytes(message.attr.ceBytes);
        }
        else if (message.attr?.$case === "ceUri") {
            obj.ceUri = message.attr.ceUri;
        }
        else if (message.attr?.$case === "ceUriRef") {
            obj.ceUriRef = message.attr.ceUriRef;
        }
        else if (message.attr?.$case === "ceTimestamp") {
            obj.ceTimestamp = message.attr.ceTimestamp.toISOString();
        }
        return obj;
    },
};
exports.CloudEventBatch = {
    fromJSON(object) {
        return {
            events: globalThis.Array.isArray(object?.events) ? object.events.map((e) => exports.CloudEvent.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.events?.length) {
            obj.events = message.events.map((e) => exports.CloudEvent.toJSON(e));
        }
        return obj;
    },
};
function bytesFromBase64(b64) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}
function base64FromBytes(arr) {
    return globalThis.Buffer.from(arr).toString("base64");
}
function fromTimestamp(t) {
    let millis = (globalThis.Number(t.seconds) || 0) * 1_000;
    millis += (t.nanos || 0) / 1_000_000;
    return new globalThis.Date(millis);
}
function fromJsonTimestamp(o) {
    if (o instanceof globalThis.Date) {
        return o;
    }
    else if (typeof o === "string") {
        return new globalThis.Date(o);
    }
    else {
        return fromTimestamp(timestamp_1.Timestamp.fromJSON(o));
    }
}
function isObject(value) {
    return typeof value === "object" && value !== null;
}
function isSet(value) {
    return value !== null && value !== undefined;
}
